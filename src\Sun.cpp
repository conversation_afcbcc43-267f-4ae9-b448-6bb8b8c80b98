#include "Sun.h"
#include <cmath>
#include <GL/glut.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

Sun::Sun(Vector3 pos, float sunSize)
    : position(pos), initialPosition(pos), size(sunSize), brightness(1.0f), rotationAngle(0.0f),
      rotationSpeed(10.0f), orbitalAngle(0.0f), orbitalSpeed(5.0f), orbitalRadius(0.0f),
      orbitalCenter(Vector3(0, 0, 0)), pulsePhase(0.0f), pulseSpeed(2.0f), coronaSize(sunSize * 1.5f) {

    // Configuração da luz solar - mais suave
    lightColor[0] = 1.0f;    // R
    lightColor[1] = 0.9f;    // G
    lightColor[2] = 0.7f;    // B
    lightColor[3] = 1.0f;    // A

    ambientLight[0] = 0.2f;
    ambientLight[1] = 0.18f;
    ambientLight[2] = 0.15f;
    ambientLight[3] = 1.0f;

    diffuseLight[0] = 0.8f;
    diffuseLight[1] = 0.7f;
    diffuseLight[2] = 0.5f;
    diffuseLight[3] = 1.0f;

    specularLight[0] = 0.6f;
    specularLight[1] = 0.6f;
    specularLight[2] = 0.5f;
    specularLight[3] = 1.0f;
}

Sun::~Sun() {
}

void Sun::update(float deltaTime) {
    rotationAngle += rotationSpeed * deltaTime;
    if (rotationAngle > 360.0f) {
        rotationAngle -= 360.0f;
    }

    // Desabilitado para evitar efeito de piscada nos asteroides
    // pulsePhase += pulseSpeed * deltaTime;
    // if (pulsePhase > 2.0f * M_PI) {
    //     pulsePhase -= 2.0f * M_PI;
    // }

    // Atualiza movimento orbital se configurado
    if (orbitalRadius > 0.0f) {
        orbitalAngle += orbitalSpeed * deltaTime;
        if (orbitalAngle > 2.0f * M_PI) {
            orbitalAngle -= 2.0f * M_PI;
        }

        // Calcula nova posição orbital
        position.x = orbitalCenter.x + cos(orbitalAngle) * orbitalRadius;
        position.z = orbitalCenter.z + sin(orbitalAngle) * orbitalRadius;
        position.y = orbitalCenter.y + sin(orbitalAngle * 0.3f) * (orbitalRadius * 0.1f); // Pequena variação vertical
    }
}

void Sun::render() {
    glPushMatrix();
    glTranslatef(position.x, position.y, position.z);
    glRotatef(rotationAngle, 0, 1, 0);

    // Desabilita iluminação para o sol (ele é auto-iluminado)
    glDisable(GL_LIGHTING);

    // Renderiza corona (halo externo)
    renderCorona();

    // Renderiza núcleo do sol
    renderSunCore();

    // Renderiza flares solares
    renderSunFlares();

    glEnable(GL_LIGHTING);
    glPopMatrix();
}

void Sun::setupLighting() {
    // DESABILITADO: Luz do sol removida para simplificar iluminação
    // Apenas uma luz branca simples (GL_LIGHT0) será usada
    /*
    float lightPos[4] = {position.x, position.y, position.z, 1.0f};

    glLightfv(GL_LIGHT2, GL_POSITION, lightPos);
    glLightfv(GL_LIGHT2, GL_AMBIENT, ambientLight);
    glLightfv(GL_LIGHT2, GL_DIFFUSE, diffuseLight);
    glLightfv(GL_LIGHT2, GL_SPECULAR, specularLight);

    glLightf(GL_LIGHT2, GL_CONSTANT_ATTENUATION, 1.0f);
    glLightf(GL_LIGHT2, GL_LINEAR_ATTENUATION, 0.0f);
    glLightf(GL_LIGHT2, GL_QUADRATIC_ATTENUATION, 0.0f);

    glEnable(GL_LIGHT2);
    */
}

void Sun::renderSunCore() {
    // Núcleo principal do sol - sem efeito de pulso
    glColor3f(1.0f, 0.8f, 0.3f);

    glutSolidSphere(size, 32, 32);

    // Camada interna mais brilhante
    glColor3f(1.0f, 1.0f, 0.8f);
    glutSolidSphere(size * 0.8f, 24, 24);
}

void Sun::renderCorona() {
    // Halo externo semi-transparente - sem efeito de pulso
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    glColor4f(1.0f, 0.6f, 0.2f, 0.3f);

    glutSolidSphere(coronaSize, 24, 24);

    glDisable(GL_BLEND);
}

void Sun::renderSunFlares() {
    // Pequenos flares ao redor do sol - sem efeito de pulso
    glPointSize(3.0f);
    glBegin(GL_POINTS);

    for (int i = 0; i < 20; i++) {
        float angle = (i / 20.0f) * 2.0f * M_PI + rotationAngle * 0.01f;
        float radius = size * 1.8f; // Raio fixo sem variação

        float x = cos(angle) * radius;
        float y = sin(angle) * radius * 0.5f;
        float z = sin(angle * 0.7f) * radius * 0.3f;

        // Intensidade fixa sem variação
        glColor3f(1.0f, 0.7f, 0.3f);

        glVertex3f(x, y, z);
    }

    glEnd();
    glPointSize(1.0f);
}

void Sun::setOrbitalMovement(float radius, float speed, const Vector3& center) {
    orbitalRadius = radius;
    orbitalSpeed = speed;
    orbitalCenter = center;
    orbitalAngle = 0.0f;

    // Define posição inicial na órbita
    if (radius > 0.0f) {
        position.x = center.x + radius;
        position.y = center.y;
        position.z = center.z;
    }
}
