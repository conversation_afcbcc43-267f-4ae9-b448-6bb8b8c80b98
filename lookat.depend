# depslib dependency file v1.0
1452511700 source:d:\ufsm\disciplinas\computacao grafica\programas\gl_7_lookat\src\lookat.cpp
	<GL/glut.h>
	<stdlib.h>
	<ctype.h>
	<stdio.h>

1426358040 d:\ufsm\disciplinas\computacao grafica\programas\gl_7_lookat\include\gl\glut.h
	"freeglut_std.h"

1437565542 d:\ufsm\disciplinas\computacao grafica\programas\gl_7_lookat\include\gl\freeglut_std.h
	<windows.h>
	<EGL/egl.h>
	<GLES/gl.h>
	<GLES2/gl2.h>
	<OpenGL/gl.h>
	<OpenGL/glu.h>
	<GL/gl.h>
	<GL/glu.h>
	<stdlib.h>

1656093241 source:d:\ufsm\disciplinas\computacao grafica\demos\gl_7_lookat\src\lookat.cpp
	<GL/glut.h>
	<stdlib.h>
	<ctype.h>
	<stdio.h>

1426372440 d:\ufsm\disciplinas\computacao grafica\demos\gl_7_lookat\include\gl\glut.h
	"freeglut_std.h"

1437579942 d:\ufsm\disciplinas\computacao grafica\demos\gl_7_lookat\include\gl\freeglut_std.h
	<windows.h>
	<EGL/egl.h>
	<GLES/gl.h>
	<GLES2/gl2.h>
	<OpenGL/gl.h>
	<OpenGL/glu.h>
	<GL/gl.h>
	<GL/glu.h>
	<stdlib.h>

1656093241 source:c:\tmp\___novo glut opengl\gl_7_lookat\src\lookat.cpp
	<GL/glut.h>
	<stdlib.h>
	<ctype.h>
	<stdio.h>

1632060799 c:\tmp\___novo glut opengl\include\gl\glut.h
	"freeglut_std.h"

1632060797 c:\tmp\___novo glut opengl\include\gl\freeglut_std.h
	<windows.h>
	<EGL/egl.h>
	<GLES/gl.h>
	<GLES2/gl2.h>
	<OpenGL/gl.h>
	<OpenGL/glu.h>
	<GL/gl.h>
	<GL/glu.h>
	<stdlib.h>

1632060799 d:\ufsm\disciplinas\computacao grafica\demos\include\gl\glut.h
	"freeglut_std.h"

1632060797 d:\ufsm\disciplinas\computacao grafica\demos\include\gl\freeglut_std.h
	<windows.h>
	<EGL/egl.h>
	<GLES/gl.h>
	<GLES2/gl2.h>
	<OpenGL/gl.h>
	<OpenGL/glu.h>
	<GL/gl.h>
	<GL/glu.h>
	<stdlib.h>

1656093241 source:d:\ufsm\disciplinas\computacao grafica\demos\8_lookat\src\lookat.cpp
	<GL/glut.h>
	<stdlib.h>
	<ctype.h>
	<stdio.h>

1751573754 source:c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\lookat.cpp
	<GL/glut.h>
	<stdlib.h>
	<ctype.h>
	<stdio.h>
	<vector>
	<ctime>
	"Config.h"
	"Vector3.h"
	"Camera.h"
	"Asteroid.h"
	"LODManager.h"
	"TextureManager.h"
	"GameMenu.h"
	"Skybox.h"
	"ProceduralAsteroid.h"
	"Sun.h"
	"Minimap.h"

1632060799 c:\users\<USER>\onedrive\documentos\dev\2025\demos\include\gl\glut.h
	"freeglut_std.h"

1632060797 c:\users\<USER>\onedrive\documentos\dev\2025\demos\include\gl\freeglut_std.h
	<windows.h>
	<EGL/egl.h>
	<GLES/gl.h>
	<GLES2/gl2.h>
	<OpenGL/gl.h>
	<OpenGL/glu.h>
	<GL/gl.h>
	<GL/glu.h>
	<stdlib.h>

1751071792 c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\config.h

1751071759 c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\vector3.h
	<cmath>

1751071770 c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\camera.h
	"Vector3.h"
	<GL/glut.h>

1751573485 c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\asteroid.h
	"Vector3.h"
	"LODManager.h"
	"TextureManager.h"
	"ProceduralAsteroid.h"
	"Mesh.h"
	<GL/glut.h>
	<cstdlib>
	<ctime>

1751072920 source:c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\lodmanager.cpp
	"LODManager.h"
	<cmath>
	<stdio.h>

1751072896 c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\lodmanager.h
	"Vector3.h"
	<GL/glut.h>

1751544614 source:c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\gamemenu.cpp
	"GameMenu.h"
	"Config.h"
	<cstdlib>

1751073344 c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\gamemenu.h
	<GL/glut.h>
	<vector>
	<string>

1751571519 c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\texturemanager.h
	<GL/glut.h>

1751571662 c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\skybox.h
	"Vector3.h"
	"Camera.h"
	"TextureManager.h"
	<GL/glut.h>
	<vector>

1751572160 source:c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\skybox.cpp
	"Skybox.h"
	<cstdlib>
	<cmath>
	<iostream>

1751571488 source:c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\texturemanager.cpp
	"TextureManager.h"
	<cmath>
	<cstdlib>
	<cstring>
	<iostream>

1751074550 c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\proceduralasteroid.h
	"Mesh.h"
	"Vector3.h"
	<map>

1751074491 c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\mesh.h
	"Vector3.h"
	<vector>
	<GL/glut.h>

1751325854 source:c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\proceduralasteroid.cpp
	"ProceduralAsteroid.h"
	<cmath>
	<cstdlib>
	<algorithm>

1751325798 source:c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\mesh.cpp
	"Mesh.h"
	<cmath>
	<algorithm>

1751573679 c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\sun.h
	"Vector3.h"
	<GL/glut.h>

1751544022 c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\minimap.h
	"Vector3.h"
	"Camera.h"
	"Asteroid.h"
	"Sun.h"
	<GL/glut.h>
	<vector>

1751544468 source:c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\minimap.cpp
	"Minimap.h"
	<cmath>
	<algorithm>

1751571203 c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\stb_image.h
	"stb_image.h"
	<stdio.h>
	<stdlib.h>
	<string.h>
	<math.h>

1751573719 source:c:\users\<USER>\onedrive\documentos\dev\2025\demos\8_gl_lookat\src\sun.cpp
	"Sun.h"
	<cmath>
	<GL/glut.h>

1751579331 source:d:\dev\2025\cg\demos\t5-murilo-leal\src\gamemenu.cpp
	"GameMenu.h"
	"Config.h"
	<cstdlib>

1751579331 d:\dev\2025\cg\demos\t5-murilo-leal\src\gamemenu.h
	<GL/glut.h>
	<vector>
	<string>

1632060799 d:\dev\2025\cg\demos\include\gl\glut.h
	"freeglut_std.h"

1632060797 d:\dev\2025\cg\demos\include\gl\freeglut_std.h
	<windows.h>
	<EGL/egl.h>
	<GLES/gl.h>
	<GLES2/gl2.h>
	<OpenGL/gl.h>
	<OpenGL/glu.h>
	<GL/gl.h>
	<GL/glu.h>
	<stdlib.h>

1751324061 d:\dev\2025\cg\demos\t5-murilo-leal\src\config.h

1751579331 source:d:\dev\2025\cg\demos\t5-murilo-leal\src\lodmanager.cpp
	"LODManager.h"
	<cmath>
	<stdio.h>

1751579331 d:\dev\2025\cg\demos\t5-murilo-leal\src\lodmanager.h
	"Vector3.h"
	<GL/glut.h>

1751324061 d:\dev\2025\cg\demos\t5-murilo-leal\src\vector3.h
	<cmath>

1751583115 source:d:\dev\2025\cg\demos\t5-murilo-leal\src\lookat.cpp
	<GL/glut.h>
	<stdlib.h>
	<ctype.h>
	<stdio.h>
	<vector>
	<ctime>
	"Config.h"
	"Vector3.h"
	"Camera.h"
	"Asteroid.h"
	"LODManager.h"
	"TextureManager.h"
	"GameMenu.h"
	"Skybox.h"
	"ProceduralAsteroid.h"
	"Sun.h"
	"Minimap.h"

1751324061 d:\dev\2025\cg\demos\t5-murilo-leal\src\camera.h
	"Vector3.h"
	<GL/glut.h>

1751579331 d:\dev\2025\cg\demos\t5-murilo-leal\src\asteroid.h
	"Vector3.h"
	"LODManager.h"
	"TextureManager.h"
	"ProceduralAsteroid.h"
	"Mesh.h"
	<GL/glut.h>
	<cstdlib>
	<ctime>

1751579331 d:\dev\2025\cg\demos\t5-murilo-leal\src\texturemanager.h
	<GL/glut.h>

1751579331 d:\dev\2025\cg\demos\t5-murilo-leal\src\proceduralasteroid.h
	"Mesh.h"
	"Vector3.h"
	<map>

1751579331 d:\dev\2025\cg\demos\t5-murilo-leal\src\mesh.h
	"Vector3.h"
	<vector>
	<GL/glut.h>

1751579331 d:\dev\2025\cg\demos\t5-murilo-leal\src\skybox.h
	"Vector3.h"
	"Camera.h"
	"TextureManager.h"
	<GL/glut.h>
	<vector>

1751579331 d:\dev\2025\cg\demos\t5-murilo-leal\src\sun.h
	"Vector3.h"
	<GL/glut.h>

1751579331 d:\dev\2025\cg\demos\t5-murilo-leal\src\minimap.h
	"Vector3.h"
	"Camera.h"
	"Asteroid.h"
	"Sun.h"
	<GL/glut.h>
	<vector>

1751579331 source:d:\dev\2025\cg\demos\t5-murilo-leal\src\mesh.cpp
	"Mesh.h"
	<cmath>
	<algorithm>

1751582432 source:d:\dev\2025\cg\demos\t5-murilo-leal\src\minimap.cpp
	"Minimap.h"
	<cmath>
	<algorithm>

1751579331 source:d:\dev\2025\cg\demos\t5-murilo-leal\src\proceduralasteroid.cpp
	"ProceduralAsteroid.h"
	<cmath>
	<cstdlib>
	<algorithm>

1751581575 source:d:\dev\2025\cg\demos\t5-murilo-leal\src\skybox.cpp
	"Skybox.h"
	<cstdlib>
	<cmath>
	<iostream>

1751583073 source:d:\dev\2025\cg\demos\t5-murilo-leal\src\sun.cpp
	"Sun.h"
	<cmath>
	<GL/glut.h>

1751579331 source:d:\dev\2025\cg\demos\t5-murilo-leal\src\texturemanager.cpp
	"TextureManager.h"
	<cmath>
	<cstdlib>
	<cstring>
	<iostream>

