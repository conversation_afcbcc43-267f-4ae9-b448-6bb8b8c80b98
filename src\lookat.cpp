/**
*   Simulador de Navegação entre Asteroides
*
*   Funcionalidades:
*   - Câmera controlável como nave espacial
*   - Sistema de asteroides 3D com iluminação
*   - Movimentação baseada em vetores
*   - Controle de FPS
*   - Alternância wireframe/preenchimento
*   - Iluminação dinâmica
*
*   Controles:
*   - WASD: Movimento da câmera
*   - Mouse: Rotação da câmera
*   - F: Wireframe/Fill
*   - R: Reset câmera
*   - ESC: Sair
*
**/

#include <GL/glut.h>
#include <stdlib.h>
#include <ctype.h>
#include <stdio.h>
#include <vector>
#include <ctime>

// Includes das classes
#include "Config.h"
#include "Vector3.h"
#include "Camera.h"
#include "Asteroid.h"
#include "LODManager.h"
#include "TextureManager.h"
#include "GameMenu.h"
#include "Skybox.h"
#include "ProceduralAsteroid.h"
#include "Sun.h"
#include "Minimap.h"

// Variáveis globais
Camera camera(Vector3(30, 10, 30)); // Posição inicial longe do sol
std::vector<Asteroid> asteroids;
LODManager lodManager;
TextureManager textureManager;
GameMenu gameMenu;
Skybox skybox;
ProceduralAsteroid asteroidGenerator;
Sun sun(Vector3(50, 30, 50), 8.0f); // Posição fixa para iluminação consistente
Minimap minimap(SCREEN_X, SCREEN_Y);
bool wireframeMode = true;
bool keys[256] = {false};
int mouseX = 0, mouseY = 0;
bool firstMouse = true;
int lastTime = 0;
float deltaTime = 0;

// Variáveis para movimento suave
Vector3 velocity(0, 0, 0);
float acceleration = 15.0f;
float friction = 8.0f;
float maxSpeed = 20.0f;

// Variáveis para controle de FPS
int frameCount = 0;
int lastFPSTime = 0;
float currentFPS = 0;
const int TARGET_FPS = 60;
const int FRAME_TIME_MS = 1000 / TARGET_FPS;

// Variáveis para loading screen
bool isLoading = true;
int loadingProgress = 0;

// Declaração das funções
void renderLoadingScreen();
void setupWireframeMode();
void setupFillMode();

void setupWireframeMode() {
    printf("=== CONFIGURANDO WIREFRAME ===\n");

    // LIMPA TODOS OS ESTADOS
    glDisable(GL_LIGHTING);
    glDisable(GL_COLOR_MATERIAL);
    glDisable(GL_LIGHT0);
    glDisable(GL_LIGHT1);
    glDisable(GL_LIGHT2);

    // FORÇA WIREFRAME
    glPolygonMode(GL_FRONT_AND_BACK, GL_LINE);

    // COR AMARELA DIRETA
    glColor3f(1.0f, 1.0f, 0.0f);

    printf("Wireframe: AMARELO configurado\n");
}

void setupFillMode() {
    printf("=== CONFIGURANDO FILL ===\n");

    // HABILITA ILUMINAÇÃO
    glEnable(GL_LIGHTING);
    glEnable(GL_COLOR_MATERIAL);
    glColorMaterial(GL_FRONT_AND_BACK, GL_AMBIENT_AND_DIFFUSE);

    // FORÇA FILL
    glPolygonMode(GL_FRONT_AND_BACK, GL_FILL);

    // LUZ BRANCA SIMPLES
    float lightPos[] = {0.0f, 100.0f, 0.0f, 1.0f};
    float white[] = {1.0f, 1.0f, 1.0f, 1.0f};
    float ambient[] = {0.3f, 0.3f, 0.3f, 1.0f};

    glLightfv(GL_LIGHT0, GL_POSITION, lightPos);
    glLightfv(GL_LIGHT0, GL_DIFFUSE, white);
    glLightfv(GL_LIGHT0, GL_AMBIENT, ambient);
    glEnable(GL_LIGHT0);

    printf("Fill: ILUMINAÇÃO BRANCA configurada\n");
}

void init() {
    printf("=== INICIANDO CONFIGURAÇÃO ===\n");

    // Inicializa random
    srand(time(NULL));

    // Configuração OpenGL básica
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    gluPerspective(60.0, (float)SCREEN_X/SCREEN_Y, 0.1, 1000.0);
    glMatrixMode(GL_MODELVIEW);
    glClearColor(0.05f, 0.05f, 0.1f, 1.0f);
    glEnable(GL_DEPTH_TEST);

    // Cria asteroides SEM loading screen (pode estar causando problema)
    asteroids.resize(NUM_ASTEROIDS);
    textureManager.initialize();
    skybox.initialize(&textureManager);

    // Gera malhas procedurais SIMPLES
    for (int i = 0; i < NUM_ASTEROIDS; i++) {
        asteroids[i].generateProceduralMesh(asteroidGenerator);
    }

    // CONFIGURAÇÃO FINAL BASEADA NO MODO
    if (wireframeMode) {
        setupWireframeMode();
    } else {
        setupFillMode();
    }

    printf("=== CONFIGURAÇÃO CONCLUÍDA ===\n");
}

void updateMovement() {
    Vector3 inputDirection(0, 0, 0);

    // Calcula direção de entrada baseada nas teclas
    if (keys['w'] || keys['W']) inputDirection = inputDirection + camera.direction;
    if (keys['s'] || keys['S']) inputDirection = inputDirection - camera.direction;
    if (keys['a'] || keys['A']) inputDirection = inputDirection - camera.right;
    if (keys['d'] || keys['D']) inputDirection = inputDirection + camera.right;
    if (keys['q'] || keys['Q']) inputDirection = inputDirection + camera.up;
    if (keys['e'] || keys['E']) inputDirection = inputDirection - camera.up;

    // Normaliza direção se há entrada
    if (inputDirection.length() > 0) {
        inputDirection = inputDirection.normalize();

        // Aplica aceleração
        velocity = velocity + inputDirection * acceleration * deltaTime;
    } else {
        // Aplica fricção quando não há entrada
        velocity = velocity * (1.0f - friction * deltaTime);
    }

    // Limita velocidade máxima
    if (velocity.length() > maxSpeed) {
        velocity = velocity.normalize() * maxSpeed;
    }

    // Aplica movimento
    Vector3 newPosition = camera.position + velocity * deltaTime;

    // Verificação básica de colisão com asteroides
    bool collision = false;
    for (const auto& asteroid : asteroids) {
        Vector3 diff = newPosition - asteroid.position;
        float distance = diff.length();
        if (distance < asteroid.size * 2.0f) { // Margem de segurança
            collision = true;
            break;
        }
    }

    // Verificação de colisão com o sol
    if (!collision) {
        Vector3 sunDiff = newPosition - sun.getPosition();
        float sunDistance = sunDiff.length();
        if (sunDistance < sun.getSize() * 2.0f) { // Margem de segurança para o sol
            collision = true;
        }
    }

    // Só aplica movimento se não houver colisão
    if (!collision) {
        camera.position = newPosition;
    } else {
        // Para a velocidade em caso de colisão
        velocity = velocity * 0.1f;
    }
}

void updateAsteroids() {
    for (auto& asteroid : asteroids) {
        asteroid.update();
    }
}

void calculateFPS() {
    frameCount++;
    int currentTime = glutGet(GLUT_ELAPSED_TIME);

    if (currentTime - lastFPSTime >= 1000) { // Atualiza FPS a cada segundo
        currentFPS = frameCount * 1000.0f / (currentTime - lastFPSTime);
        frameCount = 0;
        lastFPSTime = currentTime;
    }
}

void updateLighting() {
    if (wireframeMode) {
        setupWireframeMode();
    } else {
        setupFillMode();
    }
}

void renderText(float x, float y, const char* text) {
    // Salva estados atuais
    GLboolean lightingEnabled = glIsEnabled(GL_LIGHTING);
    GLboolean depthTestEnabled = glIsEnabled(GL_DEPTH_TEST);

    glDisable(GL_LIGHTING);
    glDisable(GL_DEPTH_TEST);

    glMatrixMode(GL_PROJECTION);
    glPushMatrix();
    glLoadIdentity();
    glOrtho(0, SCREEN_X, 0, SCREEN_Y, -1, 1);

    glMatrixMode(GL_MODELVIEW);
    glPushMatrix();
    glLoadIdentity();

    glColor3f(1.0f, 1.0f, 1.0f); // Sempre branco para texto
    glRasterPos2f(x, y);

    for (const char* c = text; *c != '\0'; c++) {
        glutBitmapCharacter(GLUT_BITMAP_HELVETICA_12, *c);
    }

    glPopMatrix();
    glMatrixMode(GL_PROJECTION);
    glPopMatrix();
    glMatrixMode(GL_MODELVIEW);

    // Restaura estados salvos
    if (depthTestEnabled) glEnable(GL_DEPTH_TEST);
    if (lightingEnabled) glEnable(GL_LIGHTING);
}

void renderLoadingScreen() {
    // SALVA ESTADOS ATUAIS
    GLboolean lightingEnabled = glIsEnabled(GL_LIGHTING);
    GLboolean depthTestEnabled = glIsEnabled(GL_DEPTH_TEST);
    GLboolean colorMaterialEnabled = glIsEnabled(GL_COLOR_MATERIAL);

    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    glDisable(GL_LIGHTING);
    glDisable(GL_DEPTH_TEST);
    glDisable(GL_COLOR_MATERIAL);

    glMatrixMode(GL_PROJECTION);
    glPushMatrix();
    glLoadIdentity();
    glOrtho(0, SCREEN_X, 0, SCREEN_Y, -1, 1);

    glMatrixMode(GL_MODELVIEW);
    glPushMatrix();
    glLoadIdentity();

    // Título
    glColor3f(1.0f, 1.0f, 1.0f);
    glRasterPos2f(SCREEN_X/2 - 100, SCREEN_Y/2 + 50);
    const char* title = "Carregando Asteroides...";
    for (const char* c = title; *c != '\0'; c++) {
        glutBitmapCharacter(GLUT_BITMAP_HELVETICA_18, *c);
    }

    // Barra de progresso
    float barWidth = 300.0f;
    float barHeight = 20.0f;
    float barX = SCREEN_X/2 - barWidth/2;
    float barY = SCREEN_Y/2;

    // Fundo da barra
    glColor3f(0.3f, 0.3f, 0.3f);
    glBegin(GL_QUADS);
        glVertex2f(barX, barY);
        glVertex2f(barX + barWidth, barY);
        glVertex2f(barX + barWidth, barY + barHeight);
        glVertex2f(barX, barY + barHeight);
    glEnd();

    // Progresso
    float progress = (float)loadingProgress / NUM_ASTEROIDS;
    glColor3f(0.0f, 0.8f, 0.0f);
    glBegin(GL_QUADS);
        glVertex2f(barX, barY);
        glVertex2f(barX + barWidth * progress, barY);
        glVertex2f(barX + barWidth * progress, barY + barHeight);
        glVertex2f(barX, barY + barHeight);
    glEnd();

    // Texto de progresso
    char progressText[50];
    sprintf(progressText, "%d / %d asteroides", loadingProgress, NUM_ASTEROIDS);
    glColor3f(1.0f, 1.0f, 1.0f);
    glRasterPos2f(SCREEN_X/2 - 60, SCREEN_Y/2 - 30);
    for (const char* c = progressText; *c != '\0'; c++) {
        glutBitmapCharacter(GLUT_BITMAP_HELVETICA_12, *c);
    }

    glPopMatrix();
    glMatrixMode(GL_PROJECTION);
    glPopMatrix();
    glMatrixMode(GL_MODELVIEW);

    // RESTAURA ESTADOS SALVOS
    if (lightingEnabled) glEnable(GL_LIGHTING);
    if (depthTestEnabled) glEnable(GL_DEPTH_TEST);
    if (colorMaterialEnabled) glEnable(GL_COLOR_MATERIAL);

    glutSwapBuffers();
}

void renderHUD() {
    char fpsText[50];
    char posText[100];
    char speedText[50];
    char lodText[100];
    char perfText[100];

    const LODStats& stats = lodManager.getStats();

    sprintf(fpsText, "FPS: %.1f", currentFPS);
    sprintf(posText, "Pos: (%.1f, %.1f, %.1f)", camera.position.x, camera.position.y, camera.position.z);
    sprintf(speedText, "Velocidade: %.1f", velocity.length());
    sprintf(lodText, "LOD: VH:%d H:%d M:%d L:%d VL:%d",
            stats.lodCounts[0], stats.lodCounts[1], stats.lodCounts[2],
            stats.lodCounts[3], stats.lodCounts[4]);
    sprintf(perfText, "Renderizados: %d | Culled: %d | Dist.Media: %.1f",
            stats.objectsRendered, stats.objectsCulled, stats.averageDistance);

    renderText(10, SCREEN_Y - 20, fpsText);
    renderText(10, SCREEN_Y - 40, posText);
    renderText(10, SCREEN_Y - 60, speedText);
    renderText(10, SCREEN_Y - 80, wireframeMode ? "Modo: Wireframe" : "Modo: Preenchido");
    renderText(10, SCREEN_Y - 100, textureManager.isEnabled() ? "Texturas: ATIVAS" : "Texturas: INATIVAS");

    int proceduralCount = 0;
    for (const auto& asteroid : asteroids) {
        if (asteroid.useProceduralMesh) proceduralCount++;
    }

    char proceduralText[100];
    sprintf(proceduralText, "Asteroides Procedurais: %d/%d", proceduralCount, NUM_ASTEROIDS);
    renderText(10, SCREEN_Y - 120, proceduralText);

    char cacheText[100];
    sprintf(cacheText, "Cache Malhas: %d", asteroidGenerator.getCacheSize());
    renderText(10, SCREEN_Y - 140, cacheText);

    renderText(10, SCREEN_Y - 160, lodText);
    renderText(10, SCREEN_Y - 180, perfText);

    if (lodManager.isDebugMode()) {
        renderText(10, SCREEN_Y - 200, "Debug LOD: ATIVO");
    }

    Asteroid* closest = nullptr;
    float closestDist = 1000000.0f;
    for (auto& asteroid : asteroids) {
        float dist = lodManager.calculateDistance(asteroid.position, camera.position);
        if (dist < closestDist) {
            closestDist = dist;
            closest = &asteroid;
        }
    }

    if (closest && closestDist < 20.0f) {
        char infoText[200];
        if (closest->useProceduralMesh) {
            sprintf(infoText, "Proximo: %s %s (%.1f)",
                ProceduralAsteroid::getShapeName(closest->proceduralParams.shape),
                ProceduralAsteroid::getCompositionName(closest->proceduralParams.composition),
                closestDist);
        } else {
            const char* types[] = {"Esfera", "Cubo", "Torus", "Teapot"};
            sprintf(infoText, "Proximo: %s Classico (%.1f)", types[closest->type], closestDist);
        }
        renderText(10, SCREEN_Y - 220, infoText);
    }
}

void display() {
    calculateFPS();

    if (isLoading) {
        renderLoadingScreen();
        return;
    }

    if (gameMenu.isMenuActive()) {
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
        gameMenu.render();
        glutSwapBuffers();
        return;
    }

    int currentTime = glutGet(GLUT_ELAPSED_TIME);
    deltaTime = (currentTime - lastTime) * 0.001f;
    lastTime = currentTime;

    if (deltaTime > 0.05f) deltaTime = 0.05f;

    updateMovement();
    updateAsteroids();

    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    glMatrixMode(GL_MODELVIEW);
    glLoadIdentity();

    // Aplica transformação da câmera
    camera.applyLookAt();

    // Atualiza iluminação após aplicar câmera
    updateLighting();

    // Renderiza skybox
    skybox.render(camera);

    // Renderiza o sol SEM configurar luz adicional
    sun.update(deltaTime);
    sun.render();

    lodManager.resetStats();

    // Renderiza asteroides com LOD e texturas
    for (auto& asteroid : asteroids) {
        asteroid.render(wireframeMode, lodManager, camera.position, &textureManager);
    }

    // Renderiza HUD
    renderHUD();

    // Renderiza minimapa
    minimap.render(camera, asteroids, sun);

    glutSwapBuffers();
}


void keyboard(unsigned char key, int x, int y) {
    if (gameMenu.isMenuActive()) {
        gameMenu.handleKeyboard(key);
        return;
    }

    keys[key] = true;

    switch(key) {
        case 27:
            gameMenu.setState(GAME_PAUSED);
            break;
        case 'f': case 'F': // Toggle wireframe/fill
            wireframeMode = !wireframeMode;
            printf("=== TOGGLE WIREFRAME ===\n");
            if (wireframeMode) {
                setupWireframeMode();
            } else {
                setupFillMode();
            }
            break;
        case 'r': case 'R': // Reset camera position
            camera = Camera(Vector3(30, 10, 30)); // Posição segura longe do sol
            velocity = Vector3(0, 0, 0); // Reset velocity também
            break;
        case '+': case '=': // Aumenta velocidade
            maxSpeed += 5.0f;
            printf("Velocidade máxima: %.1f\n", maxSpeed);
            break;
        case '-': case '_': // Diminui velocidade
            maxSpeed = maxSpeed > 5.0f ? maxSpeed - 5.0f : 5.0f;
            printf("Velocidade máxima: %.1f\n", maxSpeed);
            break;
        case 'h': case 'H': // Toggle HUD
            // Implementar toggle do HUD se necessário
            break;
        case 'l': case 'L': // Toggle LOD debug
            lodManager.setDebugMode(!lodManager.isDebugMode());
            printf("LOD Debug: %s\n", lodManager.isDebugMode() ? "ATIVO" : "INATIVO");
            break;
        case '1': // Configuração LOD próxima
            {
                LODConfig config = lodManager.getConfig();
                for (int i = 0; i < 5; i++) {
                    config.distances[i] *= 0.7f; // Reduz distâncias
                }
                lodManager.setConfig(config);
                printf("LOD: Configuração PRÓXIMA ativada\n");
            }
            break;
        case '2': // Configuração LOD média
            {
                LODConfig config;
                lodManager.setConfig(config); // Usa configuração padrão
                printf("LOD: Configuração PADRÃO ativada\n");
            }
            break;
        case '3': // Configuração LOD distante
            {
                LODConfig config = lodManager.getConfig();
                for (int i = 0; i < 5; i++) {
                    config.distances[i] *= 1.5f; // Aumenta distâncias
                }
                lodManager.setConfig(config);
                printf("LOD: Configuração DISTANTE ativada\n");
            }
            break;
        case 't': case 'T':
            textureManager.setEnabled(!textureManager.isEnabled());
            printf("Texturas: %s\n", textureManager.isEnabled() ? "ATIVADAS" : "DESATIVADAS");
            break;

        case 'g': case 'G':
            for (auto& asteroid : asteroids) {
                asteroid.generateProceduralMesh(asteroidGenerator);
            }
            printf("Asteroides regenerados proceduralmente\n");
            break;
        case 'm': case 'M': // Toggle minimapa
            minimap.setEnabled(!minimap.isEnabled());
            printf("Minimapa: %s\n", minimap.isEnabled() ? "ATIVO" : "INATIVO");
            break;
        case 'k': case 'K': // Toggle skybox
            skybox.setEnabled(!skybox.isEnabled());
            printf("Skybox: %s\n", skybox.isEnabled() ? "ATIVO" : "INATIVO");
            break;

    }
}

void keyboardUp(unsigned char key, int x, int y) {
    if (!gameMenu.isMenuActive()) {
        keys[key] = false;
    }
}

void reshape(int width, int height) {
    glViewport(0, 0, width, height);

    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    gluPerspective(45.0, (double)width / (double)height, 0.1, 1000.0);

    glMatrixMode(GL_MODELVIEW);

    // Atualiza o minimapa com o novo tamanho da tela
    minimap.updateScreenSize(width, height);
}

void specialKeys(int key, int x, int y) {
    if (gameMenu.isMenuActive()) {
        gameMenu.handleSpecialKeys(key);
    }
}

void mouseMotion(int x, int y) {
    if (gameMenu.isMenuActive()) return;

    static int centerX = SCREEN_X / 2;
    static int centerY = SCREEN_Y / 2;

    if (firstMouse) {
        firstMouse = false;
        return;
    }

    float deltaX = (x - centerX) * ROTATION_SPEED * 2.0f;
    float deltaY = (centerY - y) * ROTATION_SPEED * 2.0f;

    camera.rotate(deltaX, deltaY);

    glutWarpPointer(centerX, centerY);
}

void mouseClick(int button, int state, int x, int y) {
    // Não precisa fazer nada especial no clique
}

void printInstructions() {
    printf("=== SIMULADOR DE NAVEGACAO ENTRE ASTEROIDES ===\n");
    printf("Controles:\n");
    printf("  WASD     - Movimento da camera (frente/tras/esquerda/direita)\n");
    printf("  QE       - Movimento vertical (cima/baixo)\n");
    printf("  Mouse    - Rotacao da camera (movimento livre)\n");
    printf("  F        - Alternar wireframe/preenchimento\n");
    printf("  R        - Reset posicao da camera\n");
    printf("  +/-      - Aumentar/diminuir velocidade maxima\n");
    printf("  L        - Toggle debug LOD (mostra nivel de cada asteroide)\n");
    printf("  1/2/3    - Configuracoes LOD (proxima/padrao/distante)\n");
    printf("  T        - Toggle texturas\n");
    printf("  G        - Regenerar asteroides procedurais\n");
    printf("  M        - Toggle minimapa\n");
    printf("  ESC      - Abrir menu de pausa\n");
    printf("\nCaracteristicas:\n");
    printf("  - Movimento suave com aceleracao e friccao\n");
    printf("  - Sol central como fonte de luz principal\n");
    printf("  - Skybox com texturas espaciais\n");
    printf("  - %d asteroides com formas variadas\n", NUM_ASTEROIDS);
    printf("  - Sistema LOD (Level of Detail) para otimizacao\n");
    printf("  - HUD com informacoes de FPS, posicao, velocidade e LOD\n");
    printf("  - Minimapa 2D mostrando posicao relativa dos objetos\n");
    printf("  - Carregamento de texturas de imagens\n");
    printf("  - Controle de camera baseado em vetores matematicos\n");
    printf("  - Culling automatico por distancia\n");
    printf("\nSistema LOD:\n");
    printf("  - 5 niveis de detalhe baseados na distancia\n");
    printf("  - Culling automatico de objetos distantes\n");
    printf("  - Estatisticas em tempo real no HUD\n");
    printf("\nNavegue pela cena como se fosse uma nave espacial!\n");
    printf("===============================================\n\n");
}

int main(int argc, char** argv) {
    glutInit(&argc, argv);
    glutInitDisplayMode(GLUT_DOUBLE | GLUT_RGB | GLUT_DEPTH);
    glutInitWindowSize(SCREEN_X, SCREEN_Y);
    glutInitWindowPosition(100, 100);
    glutCreateWindow("Simulador de Navegacao entre Asteroides");

    init();
    printInstructions();

    // Registra callbacks
    glutDisplayFunc(display);
    glutIdleFunc(display);
    glutReshapeFunc(reshape);
    glutKeyboardFunc(keyboard);
    glutKeyboardUpFunc(keyboardUp);
    glutSpecialFunc(specialKeys);
    glutMotionFunc(mouseMotion);
    glutPassiveMotionFunc(mouseMotion);
    glutMouseFunc(mouseClick);

    glutSetCursor(GLUT_CURSOR_LEFT_ARROW);

    // Inicializa timer
    lastTime = glutGet(GLUT_ELAPSED_TIME);

    glutMainLoop();
    return 0;
}
